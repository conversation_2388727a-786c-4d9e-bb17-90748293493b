<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.culture.mapper.CultureMapper">

    <resultMap type="Culture" id="CultureResult">
        <id property="cultureId" column="culture_id"/>
        <result property="cultureName" column="culture_name"/>
        <result property="cultureBirthplace" column="culture_birthplace"/>
        <result property="cultureType" column="culture_type"/>
        <result property="cultureLevel" column="culture_level"/>
        <result property="cultureHeir" column="culture_heir"/>
        <result property="cultureImage" column="culture_image"/>
        <result property="cultureDescription" column="culture_decription"/>
    </resultMap>

    <!-- 获取所有非遗文化 -->
    <select id="getAllCultures" resultMap="CultureResult">
        SELECT * FROM ich_culture
    </select>

    <!-- 根据非遗ID获取非遗文化信息 -->
    <select id="getCultureById" parameterType="Long" resultMap="CultureResult">
        SELECT * FROM ich_culture WHERE culture_id = #{cultureId}
    </select>

    <!-- 添加非遗文化 -->
    <insert id="addCulture" parameterType="Culture">
        INSERT INTO ich_culture (culture_name, culture_birthplace, culture_type, culture_level, culture_heir, culture_image, culture_decription)
        VALUES (#{cultureName}, #{cultureBirthplace}, #{cultureType}, #{cultureLevel}, #{cultureHeir}, #{cultureImage}, #{cultureDescription})
    </insert>

    <!-- 更新非遗文化信息 -->
    <update id="updateCulture" parameterType="Culture">
        UPDATE ich_culture SET culture_name = #{cultureName}, culture_birthplace = #{cultureBirthplace},
                               culture_type = #{cultureType}, culture_level = #{cultureLevel},
                               culture_heir = #{cultureHeir}, culture_image = #{cultureImage},
                               culture_decription = #{cultureDescription}
        WHERE culture_id = #{cultureId}
    </update>

    <!-- 删除非遗文化 -->
    <delete id="deleteCulture" parameterType="Long">
        DELETE FROM ich_culture WHERE culture_id = #{cultureId}
    </delete>

</mapper>
