<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.culture.mapper.ExhibitionMapper">

    <resultMap type="Exhibition" id="ExhibitionResult">
        <id property="exhibitionId" column="exhibition_id"/>
        <result property="cultureId" column="culture_id"/>
        <result property="exhibitionName" column="exhibition_name"/>
        <result property="exhibitionDate" column="exhibition_date"/>
        <result property="exhibitionLocation" column="exhibition_location"/>
        <result property="exhibitionDescription" column="exhibition_description"/>
    </resultMap>

    <!-- 获取所有非遗展览 -->
    <select id="getAllExhibitions" resultMap="ExhibitionResult">
        SELECT * FROM ich_exhibition
    </select>

    <!-- 根据展览ID获取展览信息 -->
    <select id="getExhibitionById" parameterType="Long" resultMap="ExhibitionResult">
        SELECT * FROM ich_exhibition WHERE exhibition_id = #{exhibitionId}
    </select>

    <!-- 添加非遗展览 -->
    <insert id="addExhibition" parameterType="Exhibition">
        INSERT INTO ich_exhibition (culture_id, exhibition_name, exhibition_date, exhibition_location, exhibition_description)
        VALUES (#{cultureId}, #{exhibitionName}, #{exhibitionDate}, #{exhibitionLocation}, #{exhibitionDescription})
    </insert>

    <!-- 更新非遗展览信息 -->
    <update id="updateExhibition" parameterType="Exhibition">
        UPDATE ich_exhibition SET culture_id = #{cultureId}, exhibition_name = #{exhibitionName},
                                  exhibition_date = #{exhibitionDate}, exhibition_location = #{exhibitionLocation},
                                  exhibition_description = #{exhibitionDescription}
        WHERE exhibition_id = #{exhibitionId}
    </update>

    <!-- 删除非遗展览 -->
    <delete id="deleteExhibition" parameterType="Long">
        DELETE FROM ich_exhibition WHERE exhibition_id = #{exhibitionId}
    </delete>

    <!-- 删除非遗展览 -->
    <delete id="deleteExhibitionByCultureId" parameterType="Long">
        DELETE FROM ich_exhibition WHERE culture_id = #{cultureId}
    </delete>

    <select id="countByCultureId" resultType="int">
        SELECT COUNT(*) FROM ich_exhibition WHERE culture_id = #{cultureId}
    </select>


</mapper>
