<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.culture.mapper.ExhibitionRegistrationMapper">

    <resultMap type="ExhibitionRegistration" id="ExhibitionRegistrationResult">
        <id property="registrationId" column="registration_id"/>
        <result property="userId" column="user_id"/>
        <result property="exhibitionId" column="exhibition_id"/>
        <result property="registrationStatus" column="registration_status"/>
        <result property="registrationTime" column="registration_time"/>
    </resultMap>

    <!-- 获取所有非遗展览报名信息 -->
    <select id="getAllExhibitionRegistrations" resultMap="ExhibitionRegistrationResult">
        SELECT * FROM ich_exhibition_registration
    </select>

    <!-- 根据报名ID获取展览报名信息 -->
    <select id="getExhibitionRegistrationById" parameterType="Long" resultMap="ExhibitionRegistrationResult">
        SELECT * FROM ich_exhibition_registration WHERE registration_id = #{registrationId}
    </select>

    <!-- 根据用户ID获取展览报名信息 -->
    <select id="getExhibitionRegistrationByUserId" parameterType="Long" resultMap="ExhibitionRegistrationResult">
        SELECT * FROM ich_exhibition_registration WHERE user_id = #{userId}
    </select>

    <!-- 根据展览ID获取展览报名信息 -->
    <select id="getExhibitionRegistrationByExhibitionId" parameterType="Long" resultMap="ExhibitionRegistrationResult">
        SELECT * FROM ich_exhibition_registration WHERE exhibition_id = #{exhibitionId}
    </select>

    <!-- 添加非遗展览报名信息 -->
    <insert id="addExhibitionRegistration" parameterType="ExhibitionRegistration">
        INSERT INTO ich_exhibition_registration (user_id, exhibition_id, registration_status, registration_time)
        VALUES (#{userId}, #{exhibitionId}, #{registrationStatus}, #{registrationTime})
    </insert>

    <!-- 删除非遗展览报名信息 -->
    <delete id="deleteExhibitionRegistration" parameterType="Long">
        DELETE FROM ich_exhibition_registration WHERE registration_id = #{registrationId}
    </delete>

    <!-- 更新非遗展览预约信息 -->
    <update id="updateExhibitionRegistration" parameterType="ExhibitionRegistration">
        UPDATE ich_exhibition_registration SET user_id = #{userId},
                                    exhibition_id = #{exhibitionId},
                                    registration_status = #{registrationStatus},
                                    registration_time = #{registrationTime}
        WHERE registration_id = #{registrationId}
    </update>

</mapper>
